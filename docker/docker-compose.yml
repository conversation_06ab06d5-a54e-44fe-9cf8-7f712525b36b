version: '3.8'

services:
  # MySQL 8 Database
  db:
    image: mysql:8.0
    container_name: vnbds_api_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: vnbds_api_development
      MYSQL_USER: vnbds_api
      MYSQL_PASSWORD: password
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./development/mysql-init:/docker-entrypoint-initdb.d
    networks:
      - vnbds_network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis 8 for caching and Action Cable
  redis:
    image: redis:8.0-alpine
    container_name: vnbds_api_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - vnbds_network
    command: redis-server --appendonly yes

  # Rails Application
  web:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: vnbds_api_web
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      RAILS_ENV: development
      DB_HOST: db
      DB_USERNAME: vnbds_api
      DB_PASSWORD: password
      REDIS_URL: redis://redis:6379/0
      RAILS_MASTER_KEY: ${RAILS_MASTER_KEY}
      BUNDLE_WITHOUT: ""
    volumes:
      - ../:/rails
      - bundle_cache:/usr/local/bundle
      - node_modules:/rails/node_modules
      - rails_cache:/rails/tmp/cache
      - rails_storage:/rails/storage
    depends_on:
      - db
      - redis
    networks:
      - vnbds_network
    stdin_open: true
    tty: true

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  bundle_cache:
    driver: local
  node_modules:
    driver: local
  rails_cache:
    driver: local
  rails_storage:
    driver: local

networks:
  vnbds_network:
    driver: bridge
