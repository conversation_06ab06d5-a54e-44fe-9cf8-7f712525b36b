# Docker Setup for VNBDS API

This directory contains Docker configuration files for running the VNBDS API application with MySQL 8 and Redis 8.

## Files Overview

- `docker-compose.yml` - Main compose file with all services
- `docker-compose.override.yml` - Development-specific overrides
- `Dockerfile` - Production-ready Dockerfile
- `Dockerfile.dev` - Development-optimized Dockerfile
- `.env.example` - Environment variables template

## Services

### Core Services
- **web** - Rails application (port 3000)
- **db** - MySQL 8.0 database (port 3306)
- **redis** - Redis 8.0 for caching and Action Cable (port 6379)

### Optional Admin Tools
- **adminer** - Database administration (port 8080)
- **redis-commander** - Redis administration (port 8081)

## Quick Start

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Set your Rails master key in .env:**
   ```bash
   # Edit .env and set RAILS_MASTER_KEY
   RAILS_MASTER_KEY=your_actual_master_key_here
   ```

3. **Start all services:**
   ```bash
   docker-compose up -d
   ```

4. **Check logs:**
   ```bash
   docker-compose logs -f web
   ```

## Development Workflow

### First Time Setup
```bash
# Build and start services
docker-compose up --build

# The application will automatically:
# - Install gems
# - Prepare the database
# - Start the Rails server
```

### Daily Development
```bash
# Start services
docker-compose up

# Stop services
docker-compose down

# Rebuild after Gemfile changes
docker-compose up --build web
```

### Database Operations
```bash
# Run migrations
docker-compose exec web rails db:migrate

# Create and seed database
docker-compose exec web rails db:setup

# Reset database
docker-compose exec web rails db:reset

# Open Rails console
docker-compose exec web rails console

# Open database console
docker-compose exec web rails dbconsole
```

### Debugging
```bash
# Access Rails container shell
docker-compose exec web bash

# View logs
docker-compose logs web
docker-compose logs db
docker-compose logs redis

# Follow logs in real-time
docker-compose logs -f web
```

## Access Points

- **Application:** http://localhost:3000
- **Database Admin (Adminer):** http://localhost:8080
- **Redis Admin:** http://localhost:8081

### Database Connection (via Adminer)
- **Server:** db
- **Username:** vnbds_api
- **Password:** password
- **Database:** vnbds_api_development

## Environment Variables

Key environment variables (set in `.env` file):

```env
# Database
DB_HOST=db
DB_USERNAME=vnbds_api
DB_PASSWORD=password
MYSQL_ROOT_PASSWORD=rootpassword

# Redis
REDIS_URL=redis://redis:6379/0

# Rails
RAILS_ENV=development
RAILS_MASTER_KEY=your_master_key_here
```

## Volumes

Persistent data is stored in Docker volumes:
- `mysql_data` - Database files
- `redis_data` - Redis persistence
- `bundle_cache` - Gem cache for faster builds
- `rails_storage` - Active Storage files

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :3306
   netstat -tulpn | grep :6379
   ```

2. **Database connection issues:**
   ```bash
   # Check if database is ready
   docker-compose exec db mysql -u root -p -e "SHOW DATABASES;"
   ```

3. **Permission issues:**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

4. **Clean restart:**
   ```bash
   # Stop and remove everything
   docker-compose down -v
   
   # Remove images
   docker-compose down --rmi all
   
   # Start fresh
   docker-compose up --build
   ```

### Reset Everything
```bash
# WARNING: This will delete all data
docker-compose down -v --rmi all
docker system prune -f
docker-compose up --build
```

## Production Notes

For production deployment:
1. Use the main `Dockerfile` (not `Dockerfile.dev`)
2. Set proper environment variables
3. Use external database and Redis services
4. Remove admin tools (adminer, redis-commander)
5. Configure proper secrets management
