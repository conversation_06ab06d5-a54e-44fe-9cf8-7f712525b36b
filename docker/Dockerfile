# syntax=docker/dockerfile:1

FROM ruby:3.4.5-slim

# Rails app lives here
WORKDIR /rails

# Install base packages for development
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y \
    curl \
    default-mysql-client \
    libjemalloc2 \
    libvips \
    build-essential \
    default-libmysqlclient-dev \
    git \
    libyaml-dev \
    pkg-config \
    vim \
    less \
    && rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Create rails user first
RUN groupadd --system --gid 1000 rails && \
    useradd rails --uid 1000 --gid 1000 --create-home --shell /bin/bash

# Set development environment and bundle path
ENV RAILS_ENV="development" \
    BUNDLE_PATH="/home/<USER>/bundle" \
    BUNDLE_USER_HOME="/home/<USER>" \
    BUNDLE_USER_CACHE="/home/<USER>/.bundle" \
    GEM_HOME="/home/<USER>/bundle" \
    PATH="/home/<USER>/bundle/bin:$PATH"

# Create bundle directory and set permissions
RUN mkdir -p /home/<USER>/bundle && \
    chown -R rails:rails /home/<USER>
    chown -R rails:rails /rails

# Switch to rails user for gem installation
USER 1000:1000

# Install application gems
COPY --chown=rails:rails Gemfile Gemfile.lock ./
RUN bundle install

# Copy application code
COPY --chown=rails:rails . .

# Adjust binfiles to be executable on Linux (as rails user)
RUN chmod +x bin/* && \
    sed -i "s/\r$//g" bin/* && \
    sed -i 's/ruby\.exe$/ruby/' bin/*

# Entrypoint prepares the database
ENTRYPOINT ["/rails/bin/docker-entrypoint"]

# Start server
EXPOSE 3000
CMD ["./bin/rails", "server", "-b", "0.0.0.0", "-p", "3000"]
